<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<header class="site-header">
    <!-- Header Top - Ust Bilgi Cubugu -->
    <div class="header-top">
        <div class="container">
            <div class="header-top-left">
                Call us toll free: <?php echo get_theme_mod('shoptimizer_phone', '****** 2000'); ?>
            </div>
            <div class="header-top-center">
                <?php echo get_theme_mod('shoptimizer_shipping_info', 'Free worldwide shipping on all orders over $50.00'); ?>
            </div>
            <div class="header-top-right">
                Theme documentation
            </div>
        </div>
    </div>

    <!-- Header Main - Logo ve Arama Bolumu -->
    <div class="header-main">
        <div class="container">
            <!-- Logo -->
            <a href="<?php echo esc_url(home_url('/')); ?>" class="logo">
                <div class="logo-icon">S</div>
                <?php echo get_theme_mod('shoptimizer_logo_text', 'SHOPTIMIZER'); ?>
            </a>

            <!-- Arama Alani -->
            <div class="search-area">
                <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                    <input type="search" class="search-input" placeholder="Urun ara..." value="<?php echo get_search_query(); ?>" name="s">
                    <?php if (class_exists('WooCommerce')): ?>
                        <input type="hidden" name="post_type" value="product">
                    <?php endif; ?>
                    <button type="submit" class="search-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </button>
                </form>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <!-- Hesabim -->
                <a href="<?php echo class_exists('WooCommerce') ? esc_url(wc_get_page_permalink('myaccount')) : '#'; ?>" class="header-action">
                    <svg class="header-action-icon" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    My Account
                </a>

                <!-- Musteri Hizmetleri -->
                <a href="#" class="header-action">
                    <svg class="header-action-icon" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.2c.27-.27.35-.67.24-1.02C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z"/>
                    </svg>
                    Customer Help
                </a>

                <!-- Sepet -->
                <a href="#" class="header-action cart-trigger" id="cart-trigger">
                    <svg class="header-action-icon" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                    </svg>
                    Checkout
                    <?php if (class_exists('WooCommerce')): ?>
                        <span class="cart-count" id="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                    <?php endif; ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Header Navigation - Ana Menu -->
    <div class="header-nav">
        <div class="container">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class' => 'main-menu',
                'container' => false,
                'fallback_cb' => 'shoptimizer_fallback_menu'
            ));
            ?>

            <!-- Sepet Bilgisi -->
            <?php if (class_exists('WooCommerce')): ?>
                <div class="cart-info" id="cart-info-nav">
                    <?php echo WC()->cart->get_cart_total(); ?> 
                    <span class="cart-count-nav">(<?php echo WC()->cart->get_cart_contents_count(); ?>)</span>
                </div>
            <?php endif; ?>
        </div>
    </div>
</header>

<!-- Sepet Sidebar -->
<div class="cart-sidebar" id="cart-sidebar">
    <div class="cart-sidebar-header">
        <h3 class="cart-sidebar-title">Sepetim</h3>
        <button class="cart-close" id="cart-close">&times;</button>
    </div>
    <div class="cart-sidebar-content" id="cart-sidebar-content">
        <?php if (class_exists('WooCommerce')): ?>
            <?php woocommerce_mini_cart(); ?>
        <?php else: ?>
            <p>WooCommerce eklentisi gereklidir.</p>
        <?php endif; ?>
    </div>
</div>

<!-- Sepet Overlay -->
<div class="cart-overlay" id="cart-overlay"></div>

<?php
// Fallback menu function
function shoptimizer_fallback_menu() {
    echo '<ul class="main-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
    if (class_exists('WooCommerce')) {
        echo '<li><a href="' . esc_url(wc_get_page_permalink('shop')) . '">Shop</a></li>';
    }
    echo '<li><a href="' . esc_url(home_url('/blog')) . '">Blog</a></li>';
    echo '<li><a href="' . esc_url(home_url('/about')) . '">About</a></li>';
    echo '<li><a href="' . esc_url(home_url('/contact')) . '">Contact</a></li>';
    echo '</ul>';
}
?>
