/**
 * Shoptimizer Pro Main JavaScript
 */

(function($) {
    'use strict';

    // DOM hazir oldugunda calistir
    $(document).ready(function() {
        initCartSidebar();
        initLazyLoading();
        initPerformanceOptimizations();
        initWooCommerceIntegration();
    });

    /**
     * Sepet Sidebar Fonksiyonlari
     */
    function initCartSidebar() {
        const cartTrigger = $('#cart-trigger');
        const cartSidebar = $('#cart-sidebar');
        const cartOverlay = $('#cart-overlay');
        const cartClose = $('#cart-close');

        // Sepet butonuna tiklandiginda sidebar ac
        cartTrigger.on('click', function(e) {
            e.preventDefault();
            openCartSidebar();
        });

        // Kapat butonuna tiklandiginda sidebar kapat
        cartClose.on('click', function() {
            closeCartSidebar();
        });

        // Overlay'e tiklandiginda sidebar kapat
        cartOverlay.on('click', function() {
            closeCartSidebar();
        });

        // ESC tusuna basildiginda sidebar kapat
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && cartSidebar.hasClass('open')) {
                closeCartSidebar();
            }
        });

        // Sepet sidebar ac
        function openCartSidebar() {
            cartSidebar.addClass('open');
            cartOverlay.addClass('active');
            $('body').addClass('cart-sidebar-open');
            
            // Sepet icerigini guncelle
            updateCartSidebarContent();
        }

        // Sepet sidebar kapat
        function closeCartSidebar() {
            cartSidebar.removeClass('open');
            cartOverlay.removeClass('active');
            $('body').removeClass('cart-sidebar-open');
        }

        // Sepet icerigini guncelle
        function updateCartSidebarContent() {
            if (typeof shoptimizer_ajax !== 'undefined') {
                $.ajax({
                    url: shoptimizer_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'get_cart_contents',
                        nonce: shoptimizer_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#cart-sidebar-content').html(response.data);
                        }
                    }
                });
            }
        }
    }

    /**
     * Lazy Loading Fonksiyonlari
     */
    function initLazyLoading() {
        // Intersection Observer destegi varsa kullan
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy-load');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });

            // Lazy load class'ina sahip resimleri gozlemle
            document.querySelectorAll('img.lazy-load').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Performans Optimizasyonlari
     */
    function initPerformanceOptimizations() {
        // Scroll performansi icin throttle
        let ticking = false;

        function updateScrollPosition() {
            // Scroll pozisyonuna gore islemler
            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }

        $(window).on('scroll', requestTick);

        // Resize performansi icin debounce
        let resizeTimer;
        $(window).on('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                // Resize islemleri
                handleResize();
            }, 250);
        });

        function handleResize() {
            // Responsive islemler
            if ($(window).width() < 768) {
                // Mobile optimizasyonlari
                $('body').addClass('mobile-view');
            } else {
                $('body').removeClass('mobile-view');
            }
        }

        // Ilk yukleme icin resize kontrolu
        handleResize();
    }

    /**
     * WooCommerce Entegrasyonu
     */
    function initWooCommerceIntegration() {
        // Sepete urun eklendiginde
        $(document.body).on('added_to_cart', function(event, fragments, cart_hash, $button) {
            updateCartCount();
            
            // Sepet sidebar'ini ac
            setTimeout(function() {
                $('#cart-trigger').trigger('click');
            }, 500);
        });

        // Sepet guncellendiginde
        $(document.body).on('updated_wc_div', function() {
            updateCartCount();
        });

        // Sepet sayisini guncelle
        function updateCartCount() {
            if (typeof shoptimizer_ajax !== 'undefined') {
                $.ajax({
                    url: shoptimizer_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'update_cart_count',
                        nonce: shoptimizer_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('.cart-count').text(response.data);
                            $('.cart-count-nav').text('(' + response.data + ')');
                        }
                    }
                });
            }
        }

        // Sepet sidebar'indaki remove butonlari
        $(document).on('click', '.cart-sidebar .remove', function(e) {
            e.preventDefault();
            const $this = $(this);
            const productKey = $this.data('product_id');
            
            // WooCommerce remove from cart
            if (typeof wc_add_to_cart_params !== 'undefined') {
                $.ajax({
                    url: wc_add_to_cart_params.wc_ajax_url.toString().replace('%%endpoint%%', 'remove_from_cart'),
                    type: 'POST',
                    data: {
                        cart_item_key: productKey
                    },
                    success: function(response) {
                        if (response && response.fragments) {
                            // Sepet fragmentlerini guncelle
                            $.each(response.fragments, function(key, value) {
                                $(key).replaceWith(value);
                            });
                            
                            updateCartCount();
                        }
                    }
                });
            }
        });
    }

    /**
     * Arama Fonksiyonlari
     */
    function initSearchFunctionality() {
        const searchInput = $('.search-input');
        let searchTimer;

        // Arama onerisi icin debounce
        searchInput.on('input', function() {
            clearTimeout(searchTimer);
            const query = $(this).val();
            
            if (query.length > 2) {
                searchTimer = setTimeout(function() {
                    // AJAX arama onerisi
                    performSearch(query);
                }, 300);
            }
        });

        function performSearch(query) {
            // Arama onerisi implementasyonu
            console.log('Searching for:', query);
        }
    }

    /**
     * Mobile Menu Fonksiyonlari
     */
    function initMobileMenu() {
        const mobileMenuToggle = $('.mobile-menu-toggle');
        const mainMenu = $('.main-menu');

        mobileMenuToggle.on('click', function() {
            mainMenu.toggleClass('active');
            $(this).toggleClass('active');
        });
    }

    // Sayfa yuklendikten sonra ek fonksiyonlari baslat
    $(window).on('load', function() {
        initSearchFunctionality();
        initMobileMenu();
        
        // Performans metrikleri
        if (window.performance && window.performance.timing) {
            const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
            console.log('Page load time:', loadTime + 'ms');
        }
    });

})(jQuery);
