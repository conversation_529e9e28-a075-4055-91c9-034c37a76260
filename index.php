<?php
/**
 * Ana template dosyasi
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php if (have_posts()) : ?>
            
            <div class="posts-grid">
                <?php while (have_posts()) : the_post(); ?>
                    
                    <article id="post-<?php the_ID(); ?>" <?php post_class('post-item'); ?>>
                        
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="post-thumbnail">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium', array('class' => 'lazy-load')); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="post-content">
                            <header class="entry-header">
                                <h2 class="entry-title">
                                    <a href="<?php the_permalink(); ?>" rel="bookmark">
                                        <?php the_title(); ?>
                                    </a>
                                </h2>
                                
                                <div class="entry-meta">
                                    <span class="posted-on">
                                        <time class="entry-date published" datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                            <?php echo esc_html(get_the_date()); ?>
                                        </time>
                                    </span>
                                    
                                    <span class="byline">
                                        <span class="author vcard">
                                            <a class="url fn n" href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                <?php echo esc_html(get_the_author()); ?>
                                            </a>
                                        </span>
                                    </span>
                                </div>
                            </header>
                            
                            <div class="entry-summary">
                                <?php the_excerpt(); ?>
                            </div>
                            
                            <footer class="entry-footer">
                                <a href="<?php the_permalink(); ?>" class="read-more">
                                    Devamini Oku
                                </a>
                            </footer>
                        </div>
                        
                    </article>
                    
                <?php endwhile; ?>
            </div>
            
            <?php
            // Sayfalama
            the_posts_pagination(array(
                'mid_size' => 2,
                'prev_text' => __('&laquo; Onceki'),
                'next_text' => __('Sonraki &raquo;'),
            ));
            ?>
            
        <?php else : ?>
            
            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title"><?php esc_html_e('Hicbir sey bulunamadi'); ?></h1>
                </header>
                
                <div class="page-content">
                    <?php if (is_home() && current_user_can('publish_posts')) : ?>
                        
                        <p><?php
                            printf(
                                wp_kses(
                                    __('Ilk yaziyi yayinlamaya hazir misiniz? <a href="%1$s">Buradan baslayin</a>.'),
                                    array(
                                        'a' => array(
                                            'href' => array(),
                                        ),
                                    )
                                ),
                                esc_url(admin_url('post-new.php'))
                            );
                        ?></p>
                        
                    <?php elseif (is_search()) : ?>
                        
                        <p><?php esc_html_e('Aradiginiz icin uzgunum, ancak aradiginiz kelimelerle eslesen hicbir sey bulunamadi. Lutfen baska anahtar kelimelerle tekrar deneyin.'); ?></p>
                        <?php get_search_form(); ?>
                        
                    <?php else : ?>
                        
                        <p><?php esc_html_e('Aradiginizi bulamiyoruz gibi gorunuyor. Belki arama yardimci olabilir.'); ?></p>
                        <?php get_search_form(); ?>
                        
                    <?php endif; ?>
                </div>
            </section>
            
        <?php endif; ?>
        
    </div>
</main>

<?php
get_sidebar();
get_footer();
?>
