<?php
/**
 * Shoptimizer Pro Theme Functions
 */

// <PERSON><PERSON> ekle
function shoptimizer_theme_support() {
    // Post thumbnails destegi
    add_theme_support('post-thumbnails');
    
    // Title tag destegi
    add_theme_support('title-tag');
    
    // HTML5 destegi
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // WooCommerce destegi
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
    
    // Menu destegi
    register_nav_menus(array(
        'primary' => 'Ana Menu',
        'footer' => 'Footer Menu'
    ));
}
add_action('after_setup_theme', 'shoptimizer_theme_support');

// CSS ve JS dosyalarini yukle
function shoptimizer_enqueue_scripts() {
    // CSS
    wp_enqueue_style('shoptimizer-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // JavaScript
    wp_enqueue_script('shoptimizer-main', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
    
    // AJAX icin localize
    wp_localize_script('shoptimizer-main', 'shoptimizer_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('shoptimizer_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'shoptimizer_enqueue_scripts');

// Widget alanlari
function shoptimizer_widgets_init() {
    register_sidebar(array(
        'name' => 'Sidebar',
        'id' => 'sidebar-1',
        'description' => 'Ana sidebar alani',
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
    
    register_sidebar(array(
        'name' => 'Footer Widget 1',
        'id' => 'footer-1',
        'description' => 'Footer birinci alan',
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="widget-title">',
        'after_title' => '</h4>',
    ));
}
add_action('widgets_init', 'shoptimizer_widgets_init');

// AJAX sepet guncelleme
function shoptimizer_update_cart_count() {
    if (class_exists('WooCommerce')) {
        $cart_count = WC()->cart->get_cart_contents_count();
        wp_send_json_success($cart_count);
    }
    wp_send_json_error('WooCommerce not active');
}
add_action('wp_ajax_update_cart_count', 'shoptimizer_update_cart_count');
add_action('wp_ajax_nopriv_update_cart_count', 'shoptimizer_update_cart_count');

// Sepet icerigini getir
function shoptimizer_get_cart_contents() {
    if (class_exists('WooCommerce')) {
        ob_start();
        woocommerce_mini_cart();
        $mini_cart = ob_get_clean();
        wp_send_json_success($mini_cart);
    }
    wp_send_json_error('WooCommerce not active');
}
add_action('wp_ajax_get_cart_contents', 'shoptimizer_get_cart_contents');
add_action('wp_ajax_nopriv_get_cart_contents', 'shoptimizer_get_cart_contents');

// Performans optimizasyonu - CSS ve JS minify
function shoptimizer_optimize_assets() {
    if (!is_admin()) {
        // CSS minify
        add_filter('style_loader_tag', 'shoptimizer_minify_css', 10, 2);
        
        // JS defer
        add_filter('script_loader_tag', 'shoptimizer_defer_scripts', 10, 2);
    }
}
add_action('init', 'shoptimizer_optimize_assets');

function shoptimizer_defer_scripts($tag, $handle) {
    if (is_admin()) {
        return $tag;
    }
    
    $defer_scripts = array('shoptimizer-main');
    
    if (in_array($handle, $defer_scripts)) {
        return str_replace(' src', ' defer src', $tag);
    }
    
    return $tag;
}

// Lazy loading icin resim atributu ekle
function shoptimizer_add_lazy_loading($attr, $attachment, $size) {
    if (!is_admin()) {
        $attr['loading'] = 'lazy';
    }
    return $attr;
}
add_filter('wp_get_attachment_image_attributes', 'shoptimizer_add_lazy_loading', 10, 3);

// WooCommerce sepet sayisini header'da goster
function shoptimizer_cart_count() {
    if (class_exists('WooCommerce')) {
        $cart_count = WC()->cart->get_cart_contents_count();
        return $cart_count;
    }
    return 0;
}

// WooCommerce sepet toplamini goster
function shoptimizer_cart_total() {
    if (class_exists('WooCommerce')) {
        return WC()->cart->get_cart_total();
    }
    return '$0.00';
}

// Tema ozellestiricisi
function shoptimizer_customize_register($wp_customize) {
    // Logo section
    $wp_customize->add_section('shoptimizer_logo', array(
        'title' => 'Logo Ayarlari',
        'priority' => 30,
    ));
    
    $wp_customize->add_setting('shoptimizer_logo_text', array(
        'default' => 'SHOPTIMIZER',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('shoptimizer_logo_text', array(
        'label' => 'Logo Metni',
        'section' => 'shoptimizer_logo',
        'type' => 'text',
    ));
    
    // Header bilgi section
    $wp_customize->add_section('shoptimizer_header_info', array(
        'title' => 'Header Bilgileri',
        'priority' => 31,
    ));
    
    $wp_customize->add_setting('shoptimizer_phone', array(
        'default' => '****** 2000',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('shoptimizer_phone', array(
        'label' => 'Telefon Numarasi',
        'section' => 'shoptimizer_header_info',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('shoptimizer_shipping_info', array(
        'default' => 'Free worldwide shipping on all orders over $50.00',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('shoptimizer_shipping_info', array(
        'label' => 'Kargo Bilgisi',
        'section' => 'shoptimizer_header_info',
        'type' => 'text',
    ));
}
add_action('customize_register', 'shoptimizer_customize_register');
?>
