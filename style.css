/*
Theme Name: Shoptimizer Pro
Description: Modern ve hizli e-ticaret temasi
Version: 1.0
Author: Custom Theme
*/

/* Reset ve <PERSON><PERSON>er */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Stilleri */
.header-top {
    background-color: #f8f8f8;
    padding: 8px 0;
    font-size: 13px;
    border-bottom: 1px solid #e0e0e0;
}

.header-top .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-top-left {
    color: #666;
}

.header-top-center {
    color: #666;
    font-weight: 500;
}

.header-top-right {
    color: #666;
}

.header-main {
    background-color: #fff;
    padding: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-main .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #ff6b35;
    text-decoration: none;
}

.logo-icon {
    width: 30px;
    height: 30px;
    background-color: #ff6b35;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.search-area {
    flex: 1;
    max-width: 500px;
    margin: 0 30px;
    position: relative;
}

.search-form {
    display: flex;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    overflow: hidden;
}

.search-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    outline: none;
    font-size: 14px;
}

.search-btn {
    background-color: #ff6b35;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background-color: #e55a2b;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #333;
    font-size: 12px;
    transition: color 0.3s;
}

.header-action:hover {
    color: #ff6b35;
}

.header-action-icon {
    width: 24px;
    height: 24px;
    margin-bottom: 4px;
    background-size: contain;
}

.header-nav {
    background-color: #333;
    padding: 0;
}

.header-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-menu li {
    position: relative;
}

.main-menu a {
    display: block;
    padding: 15px 20px;
    color: #fff;
    text-decoration: none;
    transition: background-color 0.3s;
}

.main-menu a:hover {
    background-color: #555;
}

.cart-info {
    color: #fff;
    padding: 15px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.cart-info:hover {
    background-color: #555;
}

/* Sepet Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background-color: #fff;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 9999;
    transition: right 0.3s ease;
    overflow-y: auto;
}

.cart-sidebar.open {
    right: 0;
}

.cart-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-sidebar-title {
    font-size: 18px;
    font-weight: bold;
}

.cart-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.cart-sidebar-content {
    padding: 20px;
}

.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0,0,0,0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive */
@media (max-width: 768px) {
    .header-top {
        display: none;
    }
    
    .header-main .container {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-area {
        margin: 0;
        max-width: 100%;
    }
    
    .header-actions {
        gap: 15px;
    }
    
    .main-menu {
        flex-direction: column;
        width: 100%;
    }
    
    .cart-sidebar {
        width: 100%;
        right: -100%;
    }
}

/* Performans Optimizasyonu */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Animasyonlar */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease;
}

/* Performans Optimizasyonlari */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* GPU hizlandirma */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Preload kritik kaynaklar */
.preload-font {
    font-display: swap;
}

/* Critical CSS - Above the fold */
.above-fold {
    contain: layout style paint;
}

/* Footer Stilleri */
.site-footer {
    background-color: #2c2c2c;
    color: #fff;
    margin-top: 50px;
}

.footer-widgets {
    padding: 50px 0;
}

.footer-widgets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.footer-widget-area .widget {
    margin-bottom: 0;
}

.footer-widget-area .widget-title {
    color: #fff;
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: bold;
}

.footer-widget-area ul {
    list-style: none;
    padding: 0;
}

.footer-widget-area ul li {
    margin-bottom: 8px;
}

.footer-widget-area ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-widget-area ul li a:hover {
    color: #ff6b35;
}

.contact-info p {
    margin-bottom: 10px;
    color: #ccc;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #444;
    color: #fff;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s;
}

.social-link:hover {
    background-color: #ff6b35;
    transform: translateY(-2px);
}

.footer-bottom {
    background-color: #1a1a1a;
    padding: 20px 0;
    border-top: 1px solid #444;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright p {
    margin: 0;
    color: #ccc;
    font-size: 14px;
}

.payment-methods {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #ccc;
    font-size: 14px;
}

.payment-icons {
    display: flex;
    gap: 10px;
}

.payment-icon {
    background-color: #444;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

/* Posts Grid */
.posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.post-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.post-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.post-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.post-content {
    padding: 20px;
}

.entry-title {
    font-size: 20px;
    margin-bottom: 10px;
}

.entry-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
}

.entry-title a:hover {
    color: #ff6b35;
}

.entry-meta {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.entry-summary {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.read-more {
    color: #ff6b35;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s;
}

.read-more:hover {
    color: #e55a2b;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 40px 0;
}

.pagination a,
.pagination span {
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
}

.pagination a:hover,
.pagination .current {
    background-color: #ff6b35;
    color: #fff;
    border-color: #ff6b35;
}

/* WooCommerce Stilleri */
.woocommerce-mini-cart {
    max-height: 400px;
    overflow-y: auto;
}

.woocommerce-mini-cart-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
    gap: 15px;
}

.woocommerce-mini-cart-item:last-child {
    border-bottom: none;
}

.mini-cart-item-image {
    flex-shrink: 0;
}

.mini-cart-item-image img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.mini-cart-item-details {
    flex: 1;
}

.mini-cart-item-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.mini-cart-item-name a {
    color: #333;
    text-decoration: none;
}

.mini-cart-item-name a:hover {
    color: #ff6b35;
}

.mini-cart-item-quantity {
    font-size: 14px;
    color: #666;
}

.mini-cart-item-remove {
    flex-shrink: 0;
}

.mini-cart-item-remove .remove {
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s;
}

.mini-cart-item-remove .remove:hover {
    background-color: #ff6b35;
    color: #fff;
}

.woocommerce-mini-cart__total {
    padding: 20px 0;
    border-top: 2px solid #e0e0e0;
    text-align: center;
    font-size: 16px;
}

.woocommerce-mini-cart__buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.woocommerce-mini-cart__buttons .button {
    flex: 1;
    padding: 12px 20px;
    text-align: center;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-view-cart {
    background-color: #f8f8f8;
    color: #333;
    border: 1px solid #e0e0e0;
}

.btn-view-cart:hover {
    background-color: #e0e0e0;
}

.btn-checkout {
    background-color: #ff6b35;
    color: #fff;
    border: 1px solid #ff6b35;
}

.btn-checkout:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
}

.woocommerce-mini-cart__empty-message {
    text-align: center;
    padding: 40px 20px;
}

.woocommerce-mini-cart__empty-message p {
    margin-bottom: 20px;
    color: #666;
}

.woocommerce-mini-cart__empty-message .button {
    background-color: #ff6b35;
    color: #fff;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
    transition: background-color 0.3s;
}

.woocommerce-mini-cart__empty-message .button:hover {
    background-color: #e55a2b;
}

/* Sidebar Acikken Body Scroll Engelle */
body.cart-sidebar-open {
    overflow: hidden;
}

/* Mobile Optimizasyonlar */
@media (max-width: 768px) {
    .footer-widgets-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .posts-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .woocommerce-mini-cart__buttons {
        flex-direction: column;
    }

    .social-links {
        justify-content: center;
    }
}
